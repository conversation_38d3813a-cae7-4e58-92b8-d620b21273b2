<script lang="ts" setup>
import DefaultText from './default-text.vue';


withDefaults(defineProps<{
  label?: string;
  labelAlign?:'left' | 'right' | 'center';
  labelWidth?: number;
  contentClass?: string | string[];
  columnFlex?: boolean;
  value?:string | number | undefined;
}>(), {
  label: '',
  labelWidth: 100,
  columnFlex: false,
  contentClass: '',
  value: undefined,
  labelAlign: undefined,
});
</script>


<template>
  <div
    class="pima-detail-pair-label-item"
    :class="{'column-flex': columnFlex}"
  >
    <div
      v-if="label"
      class="label"
      :style="{'width': `${labelWidth}px`}"
      :class="labelAlign ? `align-${labelAlign}` : '' "
      v-text="label"
    />
    <div
      class="content"
      :class="contentClass"
      :style="{'width': columnFlex ? '100%' : `calc(100% - ${labelWidth}px)`}"
    >
      <slot />

      <DefaultText
        v-if="!$slots.default"
        :text="value"
      />
    </div>
  </div>
</template>
