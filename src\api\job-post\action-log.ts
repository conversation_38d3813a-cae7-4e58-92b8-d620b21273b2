import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';
import { CommonListApi } from '../common/common-list-api';


enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
}

export class ActionLogApi<T> extends CommonListApi<T> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-posts/${this.id}/action-logs`;
  }

  defaultParams() {
    return {
      page: 1,
      limit: 9999,
    };
  }


  async send(): Promise<ICommonList<T>> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.actionLog');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
