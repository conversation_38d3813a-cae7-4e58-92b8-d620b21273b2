import _ from 'lodash';

import { CommonApi } from '@/api/common/common-api';
import { type PaginationParamsOption, paginationParams } from '@/helps/api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

interface ICommonOtherList<T> extends ICommonList<T> {
  other: Record<string, unknown>;
}

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
}

export class DeliveryListApi<T> extends CommonApi<ICommonOtherList<T>> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-post-resumes/${this.id}`;
  }


  set params(value: PaginationParamsOption) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    super.params = paginationParams(value);
  }

  async send() : Promise<ICommonOtherList<T>> {
    try {
      const res = await super.send();
      const data: T[] = _.get(res, 'data', []);
      const total: number = _.get(res, 'total', 0);
      const other: Record<string, unknown> = _.get(res, 'other', {});
      return { data, total, other };
    } catch (error) {
      const t = namespaceT('apiErrors.common');
      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
