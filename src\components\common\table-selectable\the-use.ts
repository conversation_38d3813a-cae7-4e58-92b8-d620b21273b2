import { ref } from 'vue';


export function theUse({ getId, onUpdate }) {
  const parseId = typeof getId === 'function' ? getId : (o) => o.id;
  const updateFn = typeof onUpdate === 'function' ? onUpdate : () => {};

  const ids = ref([]);
  const idMap = new Map();

  function isSameBaseArray(arr1, arr2) {
    if (!(Array.isArray(arr1) && Array.isArray(arr2) && arr1.length === arr2.length)) {
      return false;
    }

    return arr1.sort().toString() === arr2.sort().toString();
  }

  function updateIds(newIds) {
    ids.value = [...newIds];
  }

  function update() {
    const nowIds = Array.from(idMap.keys());
    if (isSameBaseArray(nowIds, ids.value)) {
      return;
    }

    updateIds(nowIds);
    updateFn(Array.from(idMap.values()));
  }

  function updateIdMapByList(list) {
    list.forEach((item) => {
      idMap.set(parseId(item), item);
    });
  }

  function isSelected(row) {
    return ids.value.includes(parseId(row));
  }

  function addSelected(...list) {
    updateIdMapByList(list);
    update();
  }

  function removeSelected(...list) {
    list.forEach((item) => {
      idMap.delete(parseId(item));
    });
    update();
  }

  // 用于selected改变时使用
  function replaceSelected(list) {
    idMap.clear();
    updateIdMapByList(list);
    updateIds(Array.from(idMap.keys()));
  }

  return {
    isSelected,
    addSelected,
    removeSelected,
    replaceSelected,
  };
}
