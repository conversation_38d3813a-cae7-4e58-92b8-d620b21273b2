<script setup lang="ts">
import { onBeforeMount, computed } from 'vue';

import DetailLabelItem from '@/components/common/detail-label-item.vue';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';

import type { JobPostDetailType } from '^/types/job-post';

import { namespaceT } from '@/helps/namespace-t';
import { useSexStore } from '@/store/data-tags/sex';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { formateToDate } from '@/views/job-post/helps/handle-api-data';


const model = defineModel<JobPostDetailType>();

const t = namespaceT('jobPost');
const postTypeStore = usePostTypeStore();
const sexStore = useSexStore();


/** 实践地址 */
const practiceAddress = computed(() => {
  const { practicePlaceAddrProvName, practicePlaceAddrCityName, practicePlaceAddrAreaName } = model.value;
  let addr = '';
  if (practicePlaceAddrProvName) {
    addr += practicePlaceAddrProvName;
  }
  if (practicePlaceAddrCityName) {
    addr += practicePlaceAddrCityName;
  }
  if (practicePlaceAddrAreaName) {
    addr += practicePlaceAddrAreaName;
  }

  return addr;
});


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
  sexStore.loadDataIfNeeded();
});

</script>


<template>
  <Row
    :gutter="24"
    class="pl-20"
  >
    <!-- 岗位名称 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.postName')"
        :value="model.jobTitle"
      />
    </Col>

    <!-- 岗位类型 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.postType')"
        :value="postTypeStore.getTextByCode(model.jobType)"
      />
    </Col>

    <!-- 研究方向 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.researchDirection')"
        :value="model.researchField"
        column-flex
      />
    </Col>

    <!-- 性别要求 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.sexRemand')"
        :value="sexStore.getTextByCode(model.sex)"
      />
    </Col>


    <!-- 所需专业 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.neededMajor')"
        :value="model.majorReq"
      />
    </Col>

    <!-- 所需人数 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.numOfPerson')"
        :value="model.recruitAmt"
      />
    </Col>

    <!-- 工作内容 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.jobContent')"
        column-flex
      >
        <PimaSanitizeHtml :inner-html="model.jobContent" />
      </DetailLabelItem>
    </Col>

    <!-- 报名截止时间 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.deadline')"
        :value="formateToDate(model.endDate)"
        column-flex
      />
    </Col>

    <!-- 其他要求 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.otherRemand')"
        :value="model.othReq"
        column-flex
      />
    </Col>

    <!-- 岗位单位 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.postUnit')"
        :value="model.baseName"
        column-flex
      />
    </Col>

    <!-- 实践地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.practiceAddress')"
        :value="practiceAddress"
        column-flex
      />
    </Col>

    <!-- 详细地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.detailedAddress')"
        :value="model.practicePlace"
        column-flex
      />
    </Col>

    <!-- 公司简介 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.companyProfile')"
        :value="model.baseSummary"
        column-flex
      />
    </Col>
  </Row>
</template>
