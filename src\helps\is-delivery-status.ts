import { DeliveryStatus } from '@/consts/resume-mgt';

/** 待筛选 */
export const isPending = (status: DeliveryStatus) => status === DeliveryStatus.PENDING;

/** 已通知面试 */
export const isInterview = (status: DeliveryStatus) => status === DeliveryStatus.RESUME_PASS;

/** 初筛不通过 */
export const isResumeReject = (status: DeliveryStatus) => status === DeliveryStatus.RESUME_REJECT;

/** 面试不通过 */
export const isInterviewReject = (status: DeliveryStatus) => status === DeliveryStatus.INTERVIEW_REJECT;

/** 录用 */
export const isAdmission = (status: DeliveryStatus) => {
  const statusList = [
    DeliveryStatus.ADMISSION_PASS,
    DeliveryStatus.ADMISSION_PENDING_APPROVAL,
    DeliveryStatus.ADMISSION_REJECT,
  ];

  return statusList.includes(status);
};
