<script setup lang="ts">
import { computed } from 'vue';


interface Props {
  text:string,
  limitLength?:number
}

const props = withDefaults(defineProps<Props>(), {
  text: '',
  limitLength: 25,
});

const isOver = computed(() => {
  return props.text.length > props.limitLength;
});

const ellipsisText = computed(() => {
  return `${props.text.slice(0, props.limitLength)}...`;
});


</script>


<template>
  <div class="text-popup">
    <span v-if="!isOver">
      {{ text }}
    </span>

    <Poptip
      v-else
      trigger="hover"
      transfer
      transfer-class-name="text-popup"
    >
      <span>{{ ellipsisText }}</span>
      <template #content>
        <p>{{ text }}</p>
      </template>
    </Poptip>
  </div>
</template>


<style lang="less">
.ivu-poptip-popper.text-popup {
  p {
    max-width: 500px;
    max-height:300px;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}
</style>
