<script setup lang="ts">
import DefaultText from '@/components/common/default-text.vue';
import { namespaceT } from '@/helps/namespace-t';
import { computed } from 'vue';

interface Props {
  area?: number;
}

const props = withDefaults(defineProps<Props>(), {
  area: undefined,
});

const t = namespaceT('labManage');
const areaText = computed(() => {
  return props.area ? t('text.area', { area: props.area }) : null;
});

</script>


<template>
  <DefaultText :text="areaText" />
</template>
