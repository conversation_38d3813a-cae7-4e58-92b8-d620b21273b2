export interface SearchSimpleModelType {

  status?: string;

  /**
     * 关键字
     */
  keyword?: string;

}

export interface SearchAdvancedModelType {
  name?:string;

}

export type TypeSearchSimpleModelType = SearchSimpleModelType;


export interface LabManageListItemType {
  id?:number;
  canUpdate?:boolean;
  canRemove?:boolean;
  labName?:string;

}

export interface LabManageTypeListItemType {

}


export interface FormModelType {

}
