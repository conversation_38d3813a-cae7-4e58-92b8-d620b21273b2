<script setup lang="ts">
import { onBeforeMount } from 'vue';

import { useJobPostPublishStatusStore } from '@/store/data-tags/job-post-publish-status';


const store = useJobPostPublishStatusStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    :loading="store.loading"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
