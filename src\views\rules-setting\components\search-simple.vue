<script setup lang="ts">
import { inject } from 'vue';

import type { SearchSimpleModelType } from '^/types/lab-manage';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectAbleStatus from '@/components/biz/select/able-status.vue';

import { MENU_NAME_INJECTION_KEY, SiderMenuCodes } from '@/config/sider-menu';
import { namespaceT } from '@/helps/namespace-t';


const emit = defineEmits<{
  'on-add': []
  'on-process-design': [],
  'on-search': []
}>();

const t = namespaceT('rulesSetting');
const model = defineModel<SearchSimpleModelType>();
const menuName = inject<GetMenuName>(MENU_NAME_INJECTION_KEY);


const emitSearch = () => {
  emit('on-search');
};


</script>


<template>
  <WrapperSearchSimple
    :title="menuName((SC:typeof SiderMenuCodes) => SC.RulesSetting)"
    advanced
  >
    <PairLabelItem>
      <SelectAbleStatus
        v-model="model.status"
        class="w-160 simple-search"
        clearable
        :placeholder="t('placeholder.status')"
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>

    <template #right>
      <Button
        v-if="$can((P)=>P.LabManage.TypeManage)"
        class="pima-btn mr-15"
        type="primary"
        @click="emit('on-process-design')"
      >
        {{ t('action.processDesign') }}
      </Button>

      <Button
        v-if="$can((P)=>P.LabManage.Add)"
        class="pima-btn"
        type="primary"
        @click="emit('on-add')"
      >
        {{ t('action.add') }}
      </Button>
    </template>
  </WrapperSearchSimple>
</template>


<style lang="less" scoped>
.btn-content{
  display: flex;
  align-items: center;

  .icon{
    color:inherit;
    font-size:24px;
  }

  img{
    width:20px;
    height:20px;
    margin-right: 4px;
  }
}

</style>
