<script setup lang="ts">
import { ref, reactive, onBeforeMount, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import ApplyForm from '../../components/apply-form.vue';


import { DetailApi } from '@/api/job-post/detail';
import { SubmitForEditApi } from '@/api/job-post/edit-submit';
import { goBack } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { createFormModel } from '../../helps/models';
import { handleDetailData, handleFormParams } from '../../helps/handle-api-data';


const router = useRouter();
const route = useRoute();
const t = namespaceT('labManage');
const tc = namespaceT('common');

const loadingSubmit = ref(false);
const loadingContent = ref(false);
const formRef = ref();
const model = reactive(createFormModel());

const routeId = computed(() => {
  return route.params.id;
});

const onGoBack = () => {
  goBack(router);
};


const onSubmit = async () => {
  try {
    loadingSubmit.value = true;
    const res = await formRef.value.validate();
    if (!res) {
      return;
    }

    const api = new SubmitForEditApi({ id: routeId.value });
    api.data = {
      ...handleFormParams(model),
    };

    await api.send();
    openToastSuccess(tc('hint.dataSaved'));
    onGoBack();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loadingSubmit.value = false;
  }
};

const fetchDetail = async () => {
  try {
    loadingContent.value = true;
    const api = new DetailApi({ id: routeId.value });
    const res = await api.sendWithSpecifyType();
    await formRef.value.resetFields();
    Object.assign(model, handleDetailData(res));
  } catch (error) {
    openToastError(error.message);
  } finally {
    loadingContent.value = false;
  }
};

onBeforeMount(() => {
  fetchDetail();
});

</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('title.edit')"
      @go-back="onGoBack"
    />

    <WrapperForm :loading="loadingContent">
      <WrapperFormTitle :title="t('title.edit')">
        <ApplyForm
          ref="formRef"
          v-model="model"
        />
      </WrapperFormTitle>

      <template
        #action
      >
        <Button
          class="pima-btn mr-15"
          type="default"
          size="large"
          :disabled="loadingSubmit"
          @click="onGoBack"
        >
          {{ tc('action.cancel') }}
        </Button>

        <Button
          class="pima-btn"
          type="primary"
          size="large"
          :loading="loadingSubmit"
          @click="onSubmit"
        >
          {{ t('action.save') }}
        </Button>
      </template>
    </WrapperForm>
  </div>
</template>
