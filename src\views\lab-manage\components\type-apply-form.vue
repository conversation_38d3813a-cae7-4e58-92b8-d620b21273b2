<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

import PimaInput from '@/components/common/pima-input.vue';
import RadioAbleStatus from '@/components/biz/radio/able-status.vue';
import PimaRemoteEditor from '@/components/common/remote/pima-remote-editor.vue';
import AttachmentUpload from '@/components/biz/attachment/attachment-upload.vue';

import type { FormModelType } from '^/types/lab-manage';

import { namespaceT } from '@/helps/namespace-t';
import { createTypeFormRules } from '../helps/rules';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';
import { RelateType } from '@/consts/relate-type';


const model = defineModel<FormModelType>();

const t = namespaceT('labManage');
const tc = namespaceT('common');
const rules = createTypeFormRules();
const formRef = ref();

const coverFormatList = ['.jpg', '.jpeg', '.png', '.gif'];
const coverObj = {
  accept: coverFormatList.join(','),
  format: coverFormatList,
  tipText: t('text.attachmentTips', { type: coverFormatList.join('、 ') }),
  relateType: RelateType.LAB_TYPE_COVER,
  validField: 'cover',
  class: 'mt-5',
};

const validate = async () => {
  const flag = await formRef.value.validate();
  if (!flag) {
    formScrollIntoError(formRef.value);
  }

  return flag;
};

const validateField = async <T=void>(fieldName:string, callback?:()=>T) => {
  console.log('%c [ fieldName ]-44', 'font-size:13px; background:#6088a4; color:#a4cce8;', fieldName);
  await formRef.value.validateField(fieldName, callback);
};

const resetFields = () => {
  formRef.value.resetFields();
};


watch(() => model.value.typeCNIntroduction, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    nextTick(() => {
      formRef.value.validateField('typeCNIntroduction');
    });
  }
});

watch(() => model.value.typeENIntroduction, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    nextTick(() => {
      formRef.value.validateField('typeENIntroduction');
    });
  }
});


defineExpose({
  validate,
  validateField,
  resetFields,
});
</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    label-position="top"
    :model="model"
    :rules="rules"
  >
    <Row
      :gutter="24"
      class="pl-15"
    >
      <!-- 类型中文名称 -->
      <Col :span="12">
        <FormItem
          prop="typeCNName"
          :label="t('label.typeCNName')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.typeCNName"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>

      <!-- 类型英文名称 -->
      <Col :span="12">
        <FormItem
          prop="typeENName"
          :label="t('label.typeENName')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.typeENName"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>

      <!-- 封面图片 -->
      <Col :span="24">
        <FormItem
          prop="cover"
          :label="t('label.cover')"
          class="no-colon"
        >
          <AttachmentUpload
            v-model="model.cover"
            v-bind="coverObj"
            @on-valid="validateField"
          />
        </FormItem>
      </Col>


      <!-- 类型中文介绍 -->
      <Col :span="24">
        <FormItem
          prop="typeCNIntroduction"
          :label="t('label.typeCNIntroduction')"
          class="no-colon"
        >
          <PimaInput
            v-show="false"
            v-model.trim="model.typeCNIntroduction"
          />

          <PimaRemoteEditor
            v-model.trim="model.typeCNIntroduction"
            class="full-width"
            :height="250"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>


      <!-- 类型英文介绍 -->
      <Col :span="24">
        <FormItem
          prop="typeENIntroduction"
          :label="t('label.typeENIntroduction')"
          class="no-colon"
        >
          <PimaInput
            v-show="false"
            v-model.trim="model.typeENIntroduction"
          />

          <PimaRemoteEditor
            v-model.trim="model.typeENIntroduction"
            class="full-width"
            :height="250"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>


      <!-- 状态 -->
      <Col :span="24">
        <FormItem
          prop="status"
          :label="t('label.status')"
          class="no-colon"
        >
          <RadioAbleStatus
            v-model="model.status"
          />
        </FormItem>
      </Col>
    </Row>
  </Form>
</template>
