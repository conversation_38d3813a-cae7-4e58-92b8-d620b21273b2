import { DeliveryStatus } from '@/consts/resume-mgt';
import { StatusColor } from '@/consts/status-color';

export const getDeliveryStatusColor = (status: DeliveryStatus) => {
  const map = new Map<DeliveryStatus, StatusColor>([
    [DeliveryStatus.PENDING, StatusColor.ORANGE],
    [DeliveryStatus.RESUME_PASS, StatusColor.GREEN],
    [DeliveryStatus.RESUME_REJECT, StatusColor.RED],
    [DeliveryStatus.INTERVIEW_REJECT, StatusColor.RED],
    [DeliveryStatus.ADMISSION_PENDING_APPROVAL, StatusColor.ORANGE],
    [DeliveryStatus.ADMISSION_PASS, StatusColor.GREEN],
    [DeliveryStatus.ADMISSION_REJECT, StatusColor.RED],
  ]);

  if (map.has(status)) {
    return map.get(status);
  }

  return '';
};
