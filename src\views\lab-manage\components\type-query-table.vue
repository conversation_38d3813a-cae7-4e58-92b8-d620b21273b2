<script lang="ts" setup>
import { getCurrentInstance, onBeforeMount } from 'vue';

import type { LabManageListItemType } from '^/types/lab-manage';
import type { Auth } from '@/config/auth';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import DefaultText from '@/components/common/default-text.vue';
import CellLabType from '@/components/biz/table-cell/lab-type.vue';
import CellNameAndTime from '@/components/biz/table-cell/name-and-time.vue';


import EditIcon from '@/assets/img/table-action/edit.png';

import { namespaceT } from '@/helps/namespace-t';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { createColumnsForType } from '../helps/columns';
import { getAbleStatusColor } from '@/helps/color/able-status';
import { getAbleStatusText } from '@/helps/i18n/able-status';


defineProps<{
  data: LabManageListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-edit': [id:number]
  'on-delete': [id:number, labName:string]
}>();


const vm = getCurrentInstance();
const t = namespaceT('labManage');
const columns = createColumnsForType();

const postTypeStore = usePostTypeStore();

const can = (type: string) => {
  return vm?.proxy?.$can?.((P: typeof Auth) => P.LabManage?.[type]) ?? false;
};


const actions = [
  {
    label: t('action.edit'),
    icon: EditIcon,
    triggerEvent: (row: LabManageListItemType) => emit('on-edit', row.id),
    can: (row: LabManageListItemType) => true || (row.canUpdate && can('Edit')),
  },
];


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
});

</script>


<template>
  <CTable
    :columns="columns"
    :data="[{id:1}]"
    :loading="loading"
  >
    <!-- 实验室类型 -->
    <template #type="{ row }">
      <CellLabType :type="'1'" />
    </template>


    <!-- 备注 -->
    <template #remark="{ row }">
      <DefaultText :text="row.remark" />
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <div
        class="pima-status"
        :class="getAbleStatusColor(row.status)"
      >
        {{ getAbleStatusText(row.status) }}
      </div>
    </template>

    <!-- 操作人/时间 -->
    <template #operatorAndTime="{ row }">
      <CellNameAndTime
        :name="row.name"
        :time="row.name"
      />
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>
</template>
