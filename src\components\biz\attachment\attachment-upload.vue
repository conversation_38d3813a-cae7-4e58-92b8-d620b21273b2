<script setup lang="ts">
import { ref, computed } from 'vue';

import AttachmentUpload from '@/components/biz/attachment/upload.vue';

import type { AttachmentVO } from '^/types/attachment';
import { UploadApi } from '@/api/dfs/upload';
import { openToastSuccess, openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';


interface Props {
  size?: number
  multiple?: boolean
  // 附件配置
  accept?: string
  format?: string[]
  maxSize?: number
  tipText?: string
  relateType?: string
  validField?:string
}

interface EmitType {
  (e: 'on-valid', val: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  attachments: () => [],
  size: 30,
  multiple: false,
  accept: 'image/*,application/pdf',
  format: () => ['.pdf', '.png', '.jpeg', '.jpg'],
  maxSize: 100,
  tipText: '',
  relateType: '',
  validField: '',
});

const emit = defineEmits<EmitType>();

const attachments = defineModel<AttachmentVO[]>({ default: [] });


const uploading = ref(false);
const tc = namespaceT('common');

// 附件配置对象
const attObj = computed(() => ({
  accept: props.accept,
  format: props.format,
  maxSize: props.maxSize,
}));


const onAttachmentUpload = async (file) => {
  try {
    // 检查文件个数限制
    if (props.multiple && attachments.value.length >= props.size) {
      openToastError(tc('error.fileLength', { length: props.size }));
      return;
    }

    uploading.value = true;

    const fd = new FormData();

    fd.append('fileData', file);
    fd.append('relateType', props.relateType);
    const api = new UploadApi();
    api.data = fd;

    const { model: data } = await api.send();

    if (props.multiple) {
      attachments.value = [...attachments.value, data];
    } else {
      attachments.value = [data];
    }

    openToastSuccess(tc('hint.uploadCompleted'));
    emit('on-valid', props.validField);
  } catch (error) {
    openToastError(error.message);
  } finally {
    uploading.value = false;
  }
};

const onAttachmentRemove = ({ id }) => {
  attachments.value = attachments.value.filter((item) => item.id !== id);
  emit('on-valid', props.validField);
};
</script>


<template>
  <AttachmentUpload
    v-model="attachments"
    :uploading="uploading"
    :access="attObj.format"
    :accept="attObj.accept"
    :limit-size="attObj.maxSize"
    @on-upload="onAttachmentUpload"
    @on-remove="onAttachmentRemove"
  >
    <template #file-desertions>
      <slot name="template" />

      <span class="tips">
        {{ tipText }}
      </span>
    </template>
  </AttachmentUpload>
</template>


<style lang="less" scoped>
.multiple-files{
  display: flex;
  flex-direction: column;
  gap:10px;
}
</style>
