<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

import PimaInput from '@/components/common/pima-input.vue';
import RadioAbleStatus from '@/components/biz/radio/able-status.vue';
import NumberInput from '@/components/biz/number/number-input.vue';
import PimaRemoteEditor from '@/components/common/remote/pima-remote-editor.vue';
import PimaRemotePeople from '@/components/common/remote/pima-remote-people.vue';
import SelectLabType from '@/components/biz/select/lab-type.vue';
import SelectManagementUnit from '@/components/biz/select/management-unit.vue';

import type { FormModelType } from '^/types/lab-manage';

import { namespaceT } from '@/helps/namespace-t';
import { createFormRules } from '../helps/rules';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';


const model = defineModel<FormModelType>();

const t = namespaceT('labManage');
const tc = namespaceT('common');
const rules = createFormRules();
const formRef = ref();

const validate = async () => {
  const flag = await formRef.value.validate();
  if (!flag) {
    formScrollIntoError(formRef.value);
  }

  return flag;
};

const validateField = async <T=void>(fieldName:string, callback?:()=>T) => {
  await formRef.value.validateField(fieldName, callback);
};

const resetFields = () => {
  formRef.value.resetFields();
};


watch(() => model.value.jobContent, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    nextTick(() => {
      formRef.value.validateField('jobContent');
    });
  }
});

const onSelectUser = () => {
  formRef.value.validateField('user');
};


defineExpose({
  validate,
  validateField,
  resetFields,
});
</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    label-position="top"
    :model="model"
    :rules="rules"
  >
    <Row
      :gutter="24"
      class="pl-15"
    >
      <!-- 实验室名称 -->
      <Col :span="12">
        <FormItem
          prop="name"
          :label="t('label.name')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>

      <!-- 实验室类型 -->
      <Col :span="12">
        <FormItem
          prop="type"
          :label="t('label.type')"
          class="no-colon"
        >
          <SelectLabType
            v-model="model.type"
            :placeholder="tc('placeholder.select')"
          />
        </FormItem>
      </Col>

      <!-- 位置 -->
      <Col :span="12">
        <FormItem
          :label="t('label.position')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.researchField"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>

      <!-- 管理单位 -->
      <Col :span="12">
        <FormItem
          prop="unitForManagement"
          :label="t('label.unitForManagement')"
          class="no-colon"
        >
          <SelectManagementUnit
            v-model="model.unitForManagement"
          />
        </FormItem>
      </Col>

      <!-- 管理员 -->
      <Col :span="12">
        <FormItem
          prop="admin"
          :label="t('label.admin')"
          class="no-colon"
        >
          <PimaRemotePeople
            v-model.trim="model.admin"
            :placeholder="tc('placeholder.input')"
            @update:model-value="validateField('admin')"
          />
        </FormItem>
      </Col>

      <!-- 负责人 -->
      <Col :span="12">
        <FormItem
          prop="principal"
          :label="t('label.principal')"
          class="no-colon"
        >
          <PimaRemotePeople
            v-model.trim="model.principal"
            :placeholder="tc('placeholder.input')"
            @update:model-value="validateField('principal')"
          />
        </FormItem>
      </Col>

      <!-- 容纳人数 -->
      <Col :span="12">
        <FormItem
          :label="t('label.capacity')"
          class="no-colon"
        >
          <NumberInput
            v-model.number="model.name"
            :style="{width: '100%'}"
            :min="0"
            :max="1000"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>

      <!-- 实验室面积 -->
      <Col :span="12">
        <FormItem
          prop="recruitAmt"
          :label="t('label.area')"
          class="no-colon"
        >
          <div class="area-input">
            <NumberInput
              v-model.number="model.recruitAmt"
              :style="{width: '100%'}"
              :min="0"
              :max="1000"
              :placeholder="tc('placeholder.input')"
            />

            <span class="area-text">{{ t('text.area') }}</span>
          </div>
        </FormItem>
      </Col>

      <!-- 简介 -->
      <Col :span="24">
        <FormItem
          prop="introduction"
          :label="t('label.introduction')"
          class="no-colon"
        >
          <PimaInput
            v-show="false"
            v-model.trim="model.introduction"
          />

          <PimaRemoteEditor
            v-model.trim="model.introduction"
            class="full-width"
            :height="250"
            :placeholder="tc('placeholder.input')"
          />
        </FormItem>
      </Col>


      <!-- 状态 -->
      <Col :span="24">
        <FormItem
          prop="status"
          :label="t('label.status')"
          class="no-colon"
        >
          <RadioAbleStatus
            v-model="model.status"
          />
        </FormItem>
      </Col>
    </Row>
  </Form>
</template>


<style lang="less" scoped>
.area-input{
  display: flex;
  flex:1;
  gap:5px;
  align-items: center;

  .area-text{
    flex-shrink: 0;
  }
}

</style>
