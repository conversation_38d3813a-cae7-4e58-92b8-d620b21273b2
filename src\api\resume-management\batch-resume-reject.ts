import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  JOB_POST_NOT_END_CAN_NOT_OPERATE = 'JOB_POST_NOT_END_CAN_NOT_OPERATE',
  JOB_APPLY_STATUS_ERROR = 'JOB_APPLY_STATUS_ERROR',

}

export class BatchResumeRejectApi extends CommonApi<void> {
  url() {
    return '/enterprise/job-post-resumes/resume-reject';
  }

  method(): RequestMethod {
    return 'POST';
  }


  async send(): Promise<void> {
    try {
      await super.send();
    } catch (error) {
      const t = namespaceT('apiErrors.resumeMgt.operateCheck');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.JOB_POST_NOT_END_CAN_NOT_OPERATE:
        case ErrorCodes.JOB_APPLY_STATUS_ERROR:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
