
<script setup lang="ts">
import { defineAsyncComponent } from 'vue';


// eslint-disable-next-line import/no-unresolved
const PimaSelectPeopleAssociative = defineAsyncComponent(() => import('pimaRemoteUI/PimaSelectPeopleAssociative'));

</script>

<template>
  <PimaSelectPeopleAssociative
    v-bind="$attrs"
  />
</template>


<style lang="less">
.engineering-theme.pima-select-people-associative .select-people-associative .header {
  height: auto;
}
</style>
