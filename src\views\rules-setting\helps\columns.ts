import type { ColumnsType } from '^/types/columns';

import { namespaceT } from '@/helps/namespace-t';

export const createColumns = (): ColumnsType[] => {
  const t = namespaceT('rulesSetting.columns');
  const tc = namespaceT('common.table');

  return [
    // 序号
    {
      title: tc('serial'),
      type: 'index',
      align: 'center',
      width: 60,
    },

    // 规则名称
    {
      title: t('ruleName'),
      slot: 'ruleName',
      minWidth: 100,
    },

    // 适用实验室
    {
      title: t('applicableLaboratories'),
      slot: 'applicableLaboratories',
      minWidth: 100,
    },

    // 实验室面积
    {
      title: t('area'),
      slot: 'area',
      minWidth: 120,
    },

    // 容纳人数
    {
      title: t('capacity'),
      slot: 'capacity',
      width: 100,
    },

    // 管理员
    {
      title: t('admin'),
      slot: 'admin',
      minWidth: 100,
    },

    // 负责人
    {
      title: t('principal'),
      slot: 'principal',
      minWidth: 100,
    },

    // 简介
    {
      title: t('introduction'),
      slot: 'introduction',
      minWidth: 100,
    },

    // 备注
    {
      title: t('remark'),
      slot: 'remark',
      minWidth: 100,
    },


    // 状态
    {
      title: t('status'),
      slot: 'status',
      width: 80,
    },

    // 操作人/时间
    {
      title: t('operatorAndTime'),
      slot: 'operatorAndTime',
      minWidth: 100,
    },


    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};
