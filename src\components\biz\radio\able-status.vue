<script setup lang="ts">
import { AbleStatus } from '@/consts/status';
import { getAbleStatusText } from '@/helps/i18n/able-status';


</script>


<template>
  <RadioGroup
    v-bind="$attrs"
    class="pima-radio-default"
  >
    <Radio
      v-for="item of Object.values(AbleStatus)"
      :key="item"
      :label="item"
    >
      {{ getAbleStatusText(item) }}
    </Radio>
  </RadioGroup>
</template>
