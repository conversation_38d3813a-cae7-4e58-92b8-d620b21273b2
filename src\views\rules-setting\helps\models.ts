import type { SearchSimpleModelType, TypeSearchSimpleModelType } from '^/types/lab-manage';

import { namespaceT } from '@/helps/namespace-t';
import { createWarningModel } from '@/helps/models';

export const createSearchSimpleModel = (): SearchSimpleModelType => {
  return {
    keyword: undefined,
    status: undefined,
  };
};

export const createSearchAdvanceModel = () => {
  return {
    name: undefined,
  };
};


export const createDeleteModel = () => {
  const t = namespaceT('labManage.modal.delete');

  return createWarningModel({
    title: t('title'),
    content: t('content'),
  });
};


export const createFormModel = () => {
  return {
    name: undefined,
  };
};


// 类型管理
export const createTypeSearchSimpleModel = ():TypeSearchSimpleModelType => createSearchSimpleModel();


export const createTypeFormModel = () => {
  return {
    typeCNName: undefined,
    typeENName: undefined,
    cover: [],
    typeCNIntroduction: undefined,
    typeENIntroduction: undefined,
    status: undefined,
  };
};
