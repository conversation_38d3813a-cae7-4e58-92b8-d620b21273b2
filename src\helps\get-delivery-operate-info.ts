import _ from 'lodash';

import { <PERSON><PERSON><PERSON> } from '@/api/resume-management/interview';
import { BatchInterviewApi } from '@/api/resume-management/batch-interview';
import { ResumeRejectApi } from '@/api/resume-management/resume-reject';
import { BatchResumeRejectApi } from '@/api/resume-management/batch-resume-reject';
import { InterviewRejectApi } from '@/api/resume-management/interview-reject';
import { BatchInterviewRejectApi } from '@/api/resume-management/batch-interview-reject';
import { InitiateAdmissionApi } from '@/api/resume-management/initiate-admission';
import { BatchInitiateAdmissionApi } from '@/api/resume-management/batch-initiate-admission';
import { DeliveryOperateType, ValidateType } from '@/consts/resume-mgt';
import { namespaceT } from './namespace-t';
import { formateToDate } from './formate-to-date-type';

export const getDeliveryModalTitle = (type:DeliveryOperateType) => {
  const t = namespaceT('resumeMgt.action');

  const map = new Map<DeliveryOperateType, string>([
    [DeliveryOperateType.NOTIFY_INTERVIEW, t(DeliveryOperateType.NOTIFY_INTERVIEW)],
    [DeliveryOperateType.BATCH_NOTIFY_INTERVIEW, t(DeliveryOperateType.NOTIFY_INTERVIEW)],

    [DeliveryOperateType.RESUME_REJECT, t(DeliveryOperateType.RESUME_REJECT)],
    [DeliveryOperateType.BATCH_RESUME_REJECT, t(DeliveryOperateType.RESUME_REJECT)],

    [DeliveryOperateType.INTERVIEW_REJECT, t(DeliveryOperateType.INTERVIEW_REJECT)],
    [DeliveryOperateType.BATCH_INTERVIEW_REJECT, t(DeliveryOperateType.INTERVIEW_REJECT)],

    [DeliveryOperateType.INITIATE_ADMISSION, t(DeliveryOperateType.INITIATE_ADMISSION)],
    [DeliveryOperateType.BATCH_INITIATE_ADMISSION, t(DeliveryOperateType.INITIATE_ADMISSION)],
  ]);

  if (map.has(type)) {
    return map.get(type);
  }

  return '';
};


export type DeliveryModalApi = typeof InterviewApi |
  typeof ResumeRejectApi |
  typeof InterviewRejectApi |
  typeof InitiateAdmissionApi;

export type DeliveryModalBatchApi = typeof BatchInterviewApi |
  typeof BatchResumeRejectApi |
  typeof BatchInterviewRejectApi |
  typeof BatchInitiateAdmissionApi;

export const getDeliveryModalApi = (type: DeliveryOperateType):DeliveryModalApi | DeliveryModalBatchApi => {
  const map = new Map([
    [DeliveryOperateType.NOTIFY_INTERVIEW, InterviewApi],
    [DeliveryOperateType.BATCH_NOTIFY_INTERVIEW, BatchInterviewApi],

    [DeliveryOperateType.RESUME_REJECT, ResumeRejectApi],
    [DeliveryOperateType.BATCH_RESUME_REJECT, BatchResumeRejectApi],

    [DeliveryOperateType.INTERVIEW_REJECT, InterviewRejectApi],
    [DeliveryOperateType.BATCH_INTERVIEW_REJECT, BatchInterviewRejectApi],

    [DeliveryOperateType.INITIATE_ADMISSION, InitiateAdmissionApi],
    [DeliveryOperateType.BATCH_INITIATE_ADMISSION, BatchInitiateAdmissionApi],
  ]);

  return map.get(type);
};


export const handleApiParams = (type:DeliveryOperateType, params) => {
  const data = _.cloneDeep(params);

  const interviewList = [
    DeliveryOperateType.NOTIFY_INTERVIEW,
    DeliveryOperateType.BATCH_NOTIFY_INTERVIEW,
  ];

  if (interviewList.includes(type)) {
    data.interviewTime = formateToDate(data.interviewTime, 'fullDateTime');
  } else {
    data.interviewTime = undefined;
    data.interviewAddress = undefined;
  }

  if (Array.isArray(data.idList) && data.idList.length > 0) {
    data.idList = data.idList.map((item) => item.id);
  }


  if (!type.startsWith('batch')) {
    return _.omit(data, 'idList');
  }

  return data;
};

export const isAdmission = (type:DeliveryOperateType) => {
  const admissionList = [
    DeliveryOperateType.INITIATE_ADMISSION,
    DeliveryOperateType.BATCH_INITIATE_ADMISSION,
  ];

  return admissionList.includes(type);
};


export const isInterview = (type:DeliveryOperateType) => {
  const interviewList = [
    DeliveryOperateType.NOTIFY_INTERVIEW,
    DeliveryOperateType.BATCH_NOTIFY_INTERVIEW,
  ];

  return interviewList.includes(type);
};


export const getCheckType = (type: DeliveryOperateType):ValidateType => {
  const map = new Map<DeliveryOperateType, ValidateType>([
    [DeliveryOperateType.NOTIFY_INTERVIEW, ValidateType.NOTIFY_INTERVIEW],
    [DeliveryOperateType.BATCH_NOTIFY_INTERVIEW, ValidateType.NOTIFY_INTERVIEW],

    [DeliveryOperateType.RESUME_REJECT, ValidateType.RESUME_REJECT],
    [DeliveryOperateType.BATCH_RESUME_REJECT, ValidateType.RESUME_REJECT],

    [DeliveryOperateType.INTERVIEW_REJECT, ValidateType.INTERVIEW_REJECT],
    [DeliveryOperateType.BATCH_INTERVIEW_REJECT, ValidateType.INTERVIEW_REJECT],

    [DeliveryOperateType.INITIATE_ADMISSION, ValidateType.INITIATE_ADMISSION],
    [DeliveryOperateType.BATCH_INITIATE_ADMISSION, ValidateType.INITIATE_ADMISSION],
  ]);

  return map.get(type);
};

/** 批量通知面试 操作 */
export const isBatchInterview = (type:DeliveryOperateType) => type === DeliveryOperateType.BATCH_NOTIFY_INTERVIEW;
/** 批量初筛不通过 操作 */
export const isBatchResumeReject = (type:DeliveryOperateType) => type === DeliveryOperateType.BATCH_RESUME_REJECT;
/** 批量面试不通过 操作 */
export const isBatchInterviewReject = (type:DeliveryOperateType) => type === DeliveryOperateType.BATCH_INTERVIEW_REJECT;
/** 批量录用 操作 */
export const isBatchInitiateAdmission = (type:DeliveryOperateType) => (
  type === DeliveryOperateType.BATCH_INITIATE_ADMISSION
);
