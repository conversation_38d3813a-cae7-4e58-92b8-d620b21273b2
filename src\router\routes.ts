import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      /** 实验室管理 */
      {
        path: 'lab-manage',
        name: RN.LabManage,
        component: () => import('@/views/lab-manage'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LabManage],
          baseAuthCodes: [Auth.LabManage.View],
        },
      },

      /** 实验室管理 - 新增 */
      {
        path: 'lab-manage/add',
        name: RN.LabManageAdd,
        component: () => import('@/views/lab-manage/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LabManage],
          baseAuthCodes: [Auth.LabManage.View],
        },
      },
      /** 实验室管理 - 编辑 */
      {
        path: 'lab-manage/:id/edit',
        name: RN.LabManageEdit,
        component: () => import('@/views/lab-manage/children/edit'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LabManage],
          baseAuthCodes: [Auth.LabManage.View],
        },
      },

      /** 实验室管理 - 类型管理 */
      {
        path: 'lab-manage/type-manage',
        name: RN.LabManageType,
        component: () => import('@/views/lab-manage/children/type-list'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LabManage],
          baseAuthCodes: [Auth.LabManage.View],
        },
      },

      /** 实验室管理 - 类型管理 - 编辑 */
      {
        path: 'lab-manage/type-manage/:id/edit',
        name: RN.LabManageTypeEdit,
        component: () => import('@/views/lab-manage/children/type-edit'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LabManage],
          baseAuthCodes: [Auth.LabManage.View],
        },
      },

      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
