import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  SUBMIT_JOB_POST_FAIL = 'SUBMIT_JOB_POST_FAIL',
  JOB_STATUS_ERROR = 'JOB_STATUS_ERROR',
  NO_TRAINING_BASE = 'NO_TRAINING_BASE',
  INVALID_FIELD = 'INVALID_FIELD',
  INIT_APPROVAL_FLOW_FAIL = 'INIT_APPROVAL_FLOW_FAIL',
}

export class SubmitApi<T> extends CommonApi<T> {
  url() {
    return '/enterprise/job-posts/submit';
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.submit');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.SUBMIT_JOB_POST_FAIL:
        case ErrorCodes.JOB_STATUS_ERROR:
        case ErrorCodes.NO_TRAINING_BASE:
        case ErrorCodes.INVALID_FIELD:
        case ErrorCodes.INIT_APPROVAL_FLOW_FAIL:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
