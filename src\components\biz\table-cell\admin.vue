<script setup lang="ts">
import InsensitiveText from '../insensitive-text.vue';

interface Props {
  name?:string;
  no?:string;
  mobile?:string;
}

withDefaults(defineProps<Props>(), {
  name: undefined,
  no: undefined,
  mobile: undefined,
});

</script>


<template>
  <div class="admin-info">
    <div class="name-no">
      {{ name|| '' }}({{ no|| '' }})
    </div>

    <InsensitiveText :text="mobile" />
  </div>
</template>
