import { SignupStatus } from '@/consts/resume-mgt';
import { StatusColor } from '@/consts/status-color';

export const getSignupStatusColor = (status: SignupStatus) => {
  const map = new Map<SignupStatus, StatusColor>([
    [SignupStatus.PENDING, StatusColor.ORANGE],
    [SignupStatus.IN_PROCESS, StatusColor.ORANGE],
    [SignupStatus.END, StatusColor.RED],
    [SignupStatus.SUSPEND, StatusColor.RED],
  ]);


  if (map.has(status)) {
    return map.get(status);
  }

  return '';
};
