import { CommonApi } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  EXPORT_DATA_LIMIT = 'EXPORT_DATA_LIMIT',
}

export class ExportApi extends CommonApi<{ model:number }> {
  url() {
    return '/enterprise/job-post-resumes/export';
  }

  async send(): Promise<{ model:number }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.recruitedStudent.export');

      switch (error.code) {
        case ErrorCodes.EXPORT_DATA_LIMIT:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
