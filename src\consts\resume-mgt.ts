/** 报名状态 */
export enum SignupStatus {
  /** 待开始 */
  PENDING = 'PENDING',
  /** 报名中 */
  IN_PROCESS = 'IN_PROCESS',
  /** 已截止 */
  END = 'END',
  /** 中止 */
  SUSPEND = 'SUSPEND',

}


export enum DeliveryStatus {
  /** 待初筛 */
  PENDING = 'PENDING',
  /** 已通知面试 */
  RESUME_PASS = 'RESUME_PASS',
  /** 初筛不通过 */
  RESUME_REJECT = 'RESUME_REJECT',
  /** 面试不通过 */
  INTERVIEW_REJECT = 'INTERVIEW_REJECT',
  /** 录用（校方待审核） */
  ADMISSION_PENDING_APPROVAL = 'ADMISSION_PENDING_APPROVAL',
  /** 录用（校方已通过） */
  ADMISSION_PASS = 'ADMISSION_PASS',
  /** 录用（校方不通过） */
  ADMISSION_REJECT = 'ADMISSION_REJECT',
}


/** 投递情况 操作类型 */
export enum DeliveryOperateType {
  /** 通知面试 */
  NOTIFY_INTERVIEW = 'interview',
  /** 批量通知面试 */
  BATCH_NOTIFY_INTERVIEW = 'batchInterview',
  /** 初筛不通过 */
  RESUME_REJECT = 'resumeReject',
  /** 批量初筛不通过 */
  BATCH_RESUME_REJECT = 'batchResumeReject',
  /** 发起录用 */
  INITIATE_ADMISSION = 'recruitPass',
  /** 批量发起录用 */
  BATCH_INITIATE_ADMISSION = 'batchInitiateAdmission',
  /** 批量面试不通过 */
  BATCH_INTERVIEW_REJECT = 'batchInterviewReject',
  /** 面试不通过 */
  INTERVIEW_REJECT = 'interviewReject',
}


/** 校验接口 type 参数 */
// NOTIFY_INTERVIEW:面试通知,RESUME_REJECT:初筛不通过,INTERVIEW_REJECT:面试不通过,INITIATE_ADMISSION:发起录用
export enum ValidateType {
  /** 面试通知 */
  NOTIFY_INTERVIEW = 'NOTIFY_INTERVIEW',
  /** 初筛不通过 */
  RESUME_REJECT = 'RESUME_REJECT',
  /** 面试不通过 */
  INTERVIEW_REJECT = 'INTERVIEW_REJECT',
  /** 发起录用 */
  INITIATE_ADMISSION = 'INITIATE_ADMISSION',
}
