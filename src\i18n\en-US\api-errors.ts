export default {
  common: {
    NO_EXISTS: '数据不存在',
    INIT_APPROVAL_FLOW_FAIL: '初始化审批流程失败',
    EXPORT_DATA_LIMIT: '导出的数据记录已超出数量限制，本系统每次最多仅允许导出1048576条数据，请重新调整所需汇出数据的数量 ！',
    CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT: '当前登录账号不是企业账号',

    JOB_POST_NOT_END_CAN_NOT_OPERATE: '报名未截止，暂不能操作',
    JOB_APPLY_STATUS_ERROR: '岗位申请状态异常，请稍后再试',

  },

  jobPost: {
    submit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      SUBMIT_JOB_POST_FAIL: '提交岗位失败',
      JOB_STATUS_ERROR: '岗位状态异常，请稍后再试',
      NO_TRAINING_BASE: '用户不存在基地信息',
      INVALID_FIELD: '字段校验错误',
      INIT_APPROVAL_FLOW_FAIL: '@:apiErrors.common.INIT_APPROVAL_FLOW_FAIL',
    },

    draft: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      NO_TRAINING_BASE: '@:apiErrors.jobPost.submit.NO_TRAINING_BASE',
      INVALID_FIELD: '@:apiErrors.jobPost.submit.INVALID_FIELD',
      UPDATE_FAIL: '更新失败',
    },

    delete: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      JOB_STATUS_ERROR: '@:apiErrors.jobPost.submit.JOB_STATUS_ERROR',
    },

    resubmit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      JOB_STATUS_ERROR: '@:apiErrors.jobPost.submit.JOB_STATUS_ERROR',
      NO_TRAINING_BASE: '@:apiErrors.jobPost.submit.NO_TRAINING_BASE',
      INVALID_FIELD: '@:apiErrors.jobPost.submit.INVALID_FIELD',
      RE_SUBMIT_JOB_POST_FAIL: '重新提交岗位失败',
      INIT_APPROVAL_FLOW_FAIL: '@:apiErrors.common.INIT_APPROVAL_FLOW_FAIL',
    },

    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    actionLog: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    export: {
      EXPORT_DATA_LIMIT: '@:apiErrors.common.EXPORT_DATA_LIMIT',
      CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT: '@:apiErrors.common.CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT',
    },

  },


  resumeMgt: {
    export: {
      EXPORT_DATA_LIMIT: '@:apiErrors.resumeMgt.export.EXPORT_DATA_LIMIT',
      CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT: '@:apiErrors.common.CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT',
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    resume: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    operateCheck: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      JOB_POST_NOT_END_CAN_NOT_OPERATE: '@:apiErrors.common.JOB_POST_NOT_END_CAN_NOT_OPERATE',
      JOB_APPLY_STATUS_ERROR: '@:apiErrors.common.JOB_APPLY_STATUS_ERROR',
      INIT_APPROVAL_FLOW_FAIL: '@:apiErrors.common.INIT_APPROVAL_FLOW_FAIL',
    },


  },
};
