import { reactive, ref, toRefs } from 'vue';
import _ from 'lodash';
import { SiderMenuCodes, SiderMenu } from '@/config/sider-menu';
import { forestNodes } from '@/utils/tree';


function createSider() {
  const state = reactive({
    menu: [],
    menuMap: new Map(),
  });
  const isShow = ref(false);


  function shutSider() {
    isShow.value = false;
  }

  function openSider() {
    isShow.value = true;
  }

  function setMenu(menu) {
    const menuMap = new Map();
    // eslint-disable-next-line no-restricted-syntax
    for (const item of forestNodes(menu)) {
      menuMap.set(item.key, item);
    }

    state.menu = menu;
    state.menuMap = menuMap;
  }

  function setMenuItemBadge(items = []) {
    if (!Array.isArray(items)) {
      return;
    }

    items.forEach(({ key, badge }) => {
      state.menu.forEach((item) => {
        if (item.key === key) {
          Object.assign(item, {
            badge,
          });
        }
      });
    });
  }

  function getRouterConfig({ key }) {
    return SiderMenu.get(key);
  }

  function getFirstRoute() {
    const firstMenuItem = _.head(state.menu);
    const firstMenuSubitem = _.head(_.get(firstMenuItem, 'children', []));
    const route = getRouterConfig(firstMenuSubitem || firstMenuItem);
    return route;
  }

  function getMenuName(fn: (codes: typeof SiderMenuCodes) => typeof SiderMenuCodes): string {
    const m = fn(SiderMenuCodes);
    return state.menuMap.get(m).title || '';
  }

  function hasMenuByActiveCodes(activeCodes = []): boolean {
    return activeCodes.some((c) => state.menuMap.has(c));
  }

  return {
    ...toRefs(state),
    isShow,

    shutSider,
    openSider,
    setMenu,
    setMenuItemBadge,
    getRouterConfig,
    getFirstRoute,
    getMenuName,
    hasMenuByActiveCodes,
  };
}

// useSider是一個單例模式，因為sider是全局唯一的
let instance;
export function useSider(): ReturnType<typeof createSider> {
  if (instance) {
    return instance;
  }

  instance = createSider();
  return instance;
}
