import { CommonModelApi } from '@/api/common/common-model-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
}

export class DetailApi<T> extends CommonModelApi<T> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-apply-rec-admissions/${this.id}`;
  }


  async sendWithSpecifyType(): Promise<T> {
    try {
      const res = await super.send();
      return res.model;
    } catch (error) {
      const t = namespaceT('apiErrors.recruitedStudent.detail');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
