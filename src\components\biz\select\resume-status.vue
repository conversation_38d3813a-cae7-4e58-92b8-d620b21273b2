<script setup lang="ts">
import { useResumeStatusStore } from '@/store/data-tags/resume-status';
import { onBeforeMount } from 'vue';


const store = useResumeStatusStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
