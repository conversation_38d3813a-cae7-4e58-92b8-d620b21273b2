<script setup lang="ts">
import { onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import SearchSimple from '../../components/type-search-simple.vue';
import QueryTable from '../../components/type-query-table.vue';

import { ListApi } from '@/api/job-post/list';
import { RouterName as RN } from '@/config/router';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';
import { push, goBack } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { handleListParams } from '../../helps/handle-api-data';
import { createSearchSimpleModel } from '../../helps/models';


defineOptions({
  name: 'LabManageTypeList',
});

const router = useRouter();
const t = namespaceT('labManage');


const loadData = useTableLoader(ListApi, handleListParams);
const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});

const onGoBack = () => {
  goBack(router);
};


const onEdit = (id:number) => {
  push(router, {
    name: RN.LabManageTypeEdit,
    params: {
      id,
    },
  });
};


onBeforeMount(() => {
  qt.load();
});

</script>

<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('title.typeManagement')"
      @go-back="onGoBack"
    />

    <SearchSimple
      v-model="qt.simpleSearchModel"
      @on-search="qt.search"
    />


    <TableScroll>
      <QueryTable
        :data="qt.table.data"
        :loading="qt.table.loading"
        @on-edit="onEdit"
      />

      <template #paginator>
        <PaginatorQt
          :query-table="qt"
        />
      </template>
    </TableScroll>
  </div>
</template>
