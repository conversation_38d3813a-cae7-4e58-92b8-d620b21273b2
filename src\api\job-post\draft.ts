import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  NO_TRAINING_BASE = 'NO_TRAINING_BASE',
  INVALID_FIELD = 'INVALID_FIELD',
  UPDATE_FAIL = 'UPDATE_FAIL',
}

export class DraftApi<T> extends CommonApi<T> {
  url() {
    return '/enterprise/job-posts/draft';
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.draft');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.NO_TRAINING_BASE:
        case ErrorCodes.INVALID_FIELD:
        case ErrorCodes.UPDATE_FAIL:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
