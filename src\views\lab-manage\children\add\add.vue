<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import ApplyForm from '../../components/apply-form.vue';

import { SubmitApi } from '@/api/job-post/submit';
import { goBack } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { createFormModel } from '../../helps/models';
import { handleFormParams } from '../../helps/handle-api-data';


const router = useRouter();
const t = namespaceT('labManage');
const tc = namespaceT('common');

const loadingSubmit = ref(false);
const formRef = ref();
const model = reactive(createFormModel());


const onGoBack = () => {
  goBack(router);
};

const onSubmit = async () => {
  try {
    loadingSubmit.value = true;
    const res = await formRef.value.validate();
    if (!res) {
      return;
    }

    const api = new SubmitApi();
    api.data = {
      ...handleFormParams(model),
    };

    await api.send();
    openToastSuccess(tc('hint.dataSaved'));
    onGoBack();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loadingSubmit.value = false;
  }
};
</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('title.add')"
      @go-back="onGoBack"
    />

    <WrapperForm>
      <WrapperFormTitle :title="t('title.add')">
        <ApplyForm
          ref="formRef"
          v-model="model"
        />
      </WrapperFormTitle>

      <template
        #action
      >
        <Button
          class="pima-btn mr-15"
          type="default"
          size="large"
          :disabled="loadingSubmit"
          @click="onGoBack"
        >
          {{ tc('action.cancel') }}
        </Button>

        <Button
          class="pima-btn"
          type="primary"
          size="large"
          :loading="loadingSubmit"
          @click="onSubmit"
        >
          {{ t('action.save') }}
        </Button>
      </template>
    </WrapperForm>
  </div>
</template>
