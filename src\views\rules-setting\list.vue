<script setup lang="ts">
import { reactive, onActivated } from 'vue';
import { useRouter } from 'vue-router';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import WarningModal from '@/components/biz/modal/warning-modal.vue';
import SearchSimple from './components/search-simple.vue';
import QueryTable from './components/query-table.vue';

import { ListApi } from '@/api/job-post/list';
import { DeleteApi } from '@/api/job-post/delete';
import { RouterName as RN } from '@/config/router';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';
import { push } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { handleListParams } from './helps/handle-api-data';
import { createDeleteModel, createSearchSimpleModel } from './helps/models';


defineOptions({
  name: 'RulesSettingList',
});

const router = useRouter();
const t = namespaceT('labManage');
const tc = namespaceT('common');

const deleteModel = reactive(createDeleteModel());

const loadData = useTableLoader(ListApi, handleListParams);
const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});


const onProcessDesign = () => {
  push(router, {
    name: RN.RulesSettingProcessDesign,
  });
};

const onAdd = () => {
  push(router, {
    name: RN.RulesSettingAdd,
  });
};

const onEdit = (id:number) => {
  push(router, {
    name: RN.RulesSettingEdit,
    params: {
      id,
    },
  });
};


const onDelete = async () => {
  try {
    deleteModel.loading = true;
    const api = new DeleteApi({ id: deleteModel.id });
    await api.send();
    openToastSuccess(tc('hint.successfullyDeleted'));

    deleteModel.visible = false;
    qt.search();
  } catch (error) {
    openToastError(error.message);
  } finally {
    deleteModel.loading = false;
  }
};

const showDeleteModal = (id:number, name:string) => {
  deleteModel.id = id;
  deleteModel.content = t('modal.delete.content', { name });
  deleteModel.visible = true;
};


onActivated(() => {
  qt.load();
});

</script>

<template>
  <SearchSimple
    v-model="qt.simpleSearchModel"
    @on-add="onAdd"
    @on-process-design="onProcessDesign"
    @on-search="qt.search"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      @on-edit="onEdit"
      @on-delete="showDeleteModal"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>

  <WarningModal
    v-model="deleteModel.visible"
    :loading="deleteModel.loading"
    :title="deleteModel.title"
    :content="deleteModel.content"
    @on-confirm="onDelete"
  />
</template>
