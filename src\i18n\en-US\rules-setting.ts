export default {
  title: {
    add: '新增预约规则',
    edit: '编辑预约规则',
    detail: '查看预约规则',
  },

  label: {
    ruleName: '规则名称',
    weekOpenTime: '每周开放时间',
    allowSkipClass: '是否允许跳课申请',
    openRange: '开放预约范围',
    cumulativeAppointmentDuration: '累计预约时长',
    allowAppointmentDuration: '允许预约时长',
    advanceAppointmentRequirements: '提前预约要求',
    cancelAppointmentRequirements: '取消预约要求',
    openScope: '开放范围',
    openDepartmentCollege: '开放部门/学院',
    automaticReturnMechanism: '自动退回机制',
    applicableLaboratories: '适用实验室',

    remark: '@:labManage.label.remark',
    status: '@:labManage.label.status',

  },

  text: {
    weekOpenTime: {
      text1: [
        '每天',
        '开放当天后',
        '天内的时段',
        '（例如：每天 9:00 后开放次日的实验室让用户进行预约，则可以设置为【每天 9:00 开放当天后 1 天内的时段】。）',
      ],
      text2: [
        '以当前时间为基准开放往后',
        '小时内的时段',
        '（默认为24小时，填0则不作限制）',
      ],
    },
    cumulativeAppointmentDuration: [
      '同一人、同一天、对同一实验室累计限制为',
      '分钟',
    ],
    allowAppointmentDuration: [
      '每人每次预约时间限制为',
      '分钟',
    ],
    advanceAppointmentRequirements: [
      '需要提前',
      '小时预约 （默认为2小时，填0则不作限制）',
    ],
    cancelAppointmentRequirements: [
      '需要提前',
      '分钟取消预约 （默认为5小时，填0则不作限制）',
    ],
    automaticReturnMechanism: {
      text1: [
        '每天',
        '前还未审批的申请则由系统自动退回',
      ],
      text2: [
        '提交预约',
        '小时内还未审批的申请则由系统自动退回（填0则不会自动退回）',
      ],
    },
    applicableLaboratories: '（红色字体实验室已设置过规则，若再次勾选，则旧规则会被替换，请道慎选择。）',
  },

  placeholder: {
    keyword: '请输入规则名称、适用实验室',
  },

  action: {
    add: '@:labManage.action.add',
    edit: '@:common.action.edit',
    delete: '@:common.action.delete',
    detail: '查看',
    processDesign: '审批流程设计',
  },
};
