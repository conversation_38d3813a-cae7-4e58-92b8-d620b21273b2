import { namespaceT } from '@/helps/namespace-t';
import { createInputRules, createSelectRules } from '@/helps/rules';
import { isBefore, subDays } from 'date-fns';

export const createFormRules = () => {
  const t = namespaceT('jobPost.error');

  return {
    /** 岗位名称 */
    jobTitle: [createInputRules()],
    /** 岗位类型 */
    jobType: [createSelectRules()],
    /** 研究方向 */
    researchField: [createInputRules()],
    /** 性别要求 */
    sex: [createSelectRules()],
    /** 所需专业 */
    majorReq: [createInputRules()],
    /** 所需人数 */
    recruitAmt: [createInputRules({ type: 'number' })],
    /** 工作内容 */
    jobContent: [createInputRules()],
    /** 实践地址 */
    practiceAddress: [createSelectRules({ type: 'array' })],
    /** 详细地址 */
    practicePlace: [createInputRules()],
    /** 报名截止时间 */
    endDate: [createSelectRules({ type: 'date' }), {
      validator(rule, value, callback) {
        if (isBefore(value, subDays(new Date(), 1))) {
          callback(new Error(t('endTimeMustAfterNow')));
        } else {
          callback();
        }
      },
      trigger: 'change',
    }],
  };
};
