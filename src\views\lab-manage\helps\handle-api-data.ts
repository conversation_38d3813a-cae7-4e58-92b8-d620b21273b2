import _ from 'lodash';

import type { SearchSimpleModelType } from '^/types/lab-manage';
import type { PaginationParamsOption } from '@/helps/api';


type ListParamsType = SearchSimpleModelType & PaginationParamsOption;


/** 处理列表接口参数 */
export const handleListParams = (params:ListParamsType) => {
  const cloneParams = _.cloneDeep(params);

  return cloneParams;
};


/** 处理表单数据 */
export const handleFormParams = (data:Record<string, unknown>) => {
  const cloneData = _.cloneDeep(data);

  return cloneData;
};


/** 详情数据处理 */
export const handleDetailData = (data:Record<string, unknown>) => {
  const cloneData = _.cloneDeep(data);

  return cloneData;
};

/** 类型管理 详情数据处理 */
export const handleTypeDetailData = (data:Record<string, unknown>) => {
  const cloneData = _.cloneDeep(data);

  cloneData.cover = cloneData.cover ? [cloneData.cover] : [];

  return cloneData;
};


/** 处理类型管理  表单数据 */
export const handleTypeFormParams = (data:Record<string, unknown>) => {
  const cloneData = _.cloneDeep(data);

  cloneData.cover = cloneData.cover[0].id;

  return cloneData;
};
