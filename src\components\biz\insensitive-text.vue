<script setup lang="ts">
import { useDesensitizeText } from '@/uses/use-desensitize-text';


interface Props {
  text?: string | string[];
}

const props = withDefaults(defineProps<Props>(), {
  text: '',
});

const { textByHandled, onToggleStatus, isEmpty } = useDesensitizeText(props.text);

</script>


<template>
  <div
    class="insensitive-text-list"
    :class="{'no-cursor': isEmpty}"
    @click="onToggleStatus"
  >
    <div
      v-for="(item, index) in textByHandled"
      :key="index"
      class="insensitive-text"
    >
      {{ item }}
    </div>
  </div>
</template>


<style lang="less" scoped>
.insensitive-text-list {
    cursor: pointer;

    &:hover{
      color: var(--primary-color);
    }

    &.no-cursor{
      cursor: auto;
    }
}
</style>
