<script setup lang="ts">
import { useJobPostStatusStore } from '@/store/data-tags/job-post-status';
import { onBeforeMount } from 'vue';


const store = useJobPostStatusStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
