type MatchPattern = string | RegExp | (string | RegExp)[];

// KeepAlive支持Router配置的二级路由，请注意，子路由是无法KeepAlive的
// Include规则
// 在 3.2.34 或以上的版本中，使用 <script setup> 的单文件组件会自动根据文件名生成对应的 name 选项，无需再手动声明。
export const KeepAliveInclude: MatchPattern = [
  'JobPostList',
  'ResumeManagementList',
  'RecruitedStudentList',

  'LabManageList',
  'RulesSettingList',
  'SpecialRulesSettingList',

];

// Exclude规则
export const KeepAliveExclude: MatchPattern = [
];
