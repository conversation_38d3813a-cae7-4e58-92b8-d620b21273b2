<script setup lang="ts">
import DefaultText from '@/components/common/default-text.vue';

interface Props {
  name?:string;
  managementUnit?:string;
  position?:string;
}

withDefaults(defineProps<Props>(), {
  name: undefined,
  managementUnit: undefined,
  position: undefined,
});

</script>


<template>
  <div class="lab-info">
    <DefaultText :text="name" />
    <br>
    <DefaultText :text="managementUnit" />
    <br>
    <DefaultText :text="position" />
  </div>
</template>
