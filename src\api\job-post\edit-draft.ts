import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_TRAINING_BASE = 'NO_TRAINING_BASE',
  INVALID_FIELD = 'INVALID_FIELD',
}

export class DraftForEditApi<T> extends CommonApi<T> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-posts/${this.id}/draft`;
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.draft');

      switch (error.code) {
        case ErrorCodes.NO_TRAINING_BASE:
        case ErrorCodes.INVALID_FIELD:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
