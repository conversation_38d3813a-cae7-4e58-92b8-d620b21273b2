import { isNothing } from '@/utils/is';
import { dateFormatSTZ } from './date';
import { namespaceT } from './namespace-t';

/**
 *  格式化时间数据格式
 * @param date 时间数据
 * @param type  默认值 date  类型参考i18n/date-format.ts
 */
export const formateToDate = (date: string | Date, type:string = 'date') => {
  if (isNothing(date)) {
    return null;
  }

  const t = namespaceT('dateFormat');
  return dateFormatSTZ(new Date(date), t(type));
};
