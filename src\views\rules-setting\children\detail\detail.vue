<script setup lang="ts">
import { ref, onBeforeMount, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailForm from '@/components/biz/job-post/detail-form.vue';
import ActionLog from '@/components/biz/action-log.vue';

import type { ActionLogType } from '^/types/action-log';

import { DetailApi } from '@/api/job-post/detail';
import { ActionLogApi } from '@/api/job-post/action-log';
import { goBack } from '@/helps/navigation';
import { openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';

import { createDetailModel } from '../../helps/models';
import { handleDetailData } from '../../helps/handle-api-data';


const route = useRoute();
const router = useRouter();
const t = namespaceT('jobPost');

const loading = ref(false);
const model = reactive(createDetailModel());
const actionLog = ref<ActionLogType[]>([]);

const routeId = computed(() => route.params.id);

const fetchDetail = async () => {
  const api = new DetailApi({ id: routeId.value });
  const res = await api.sendWithSpecifyType();
  Object.assign(model, handleDetailData(res, true));
};


const fetchActionLogs = async () => {
  const api = new ActionLogApi<ActionLogType>({ id: routeId.value });
  const res = await api.send();
  actionLog.value = res.data;
};

const fetchAll = async () => {
  try {
    loading.value = true;
    await Promise.all([fetchDetail(), fetchActionLogs()]);
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};


onBeforeMount(() => {
  fetchAll();
});
</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('title.detail')"
      @go-back="goBack(router)"
    />
    <WrapperForm :loading="loading">
      <NavFormWrap :nav-bar="null">
        <WrapperFormTitle :title="t('title.detail')">
          <DetailForm
            v-model="model"
          />
        </WrapperFormTitle>

        <template #right>
          <ActionLog
            :data-source="actionLog"
            :title="t('actionLog.title')"
            :empty-text="t('actionLog.emptyText',{title:t('actionLog.title')})"
          />
        </template>
      </NavFormWrap>
    </WrapperForm>
  </div>
</template>
