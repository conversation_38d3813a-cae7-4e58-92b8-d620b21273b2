<script setup lang="ts">
import type { SearchSimpleModelType } from '^/types/lab-manage';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectAbleStatus from '@/components/biz/select/able-status.vue';

import { namespaceT } from '@/helps/namespace-t';


const emit = defineEmits<{
  'on-search': []
}>();

const t = namespaceT('labManage');
const model = defineModel<SearchSimpleModelType>();


const emitSearch = () => {
  emit('on-search');
};


</script>


<template>
  <WrapperSearchSimple>
    <PairLabelItem>
      <SelectAbleStatus
        v-model="model.status"
        class="w-160 simple-search"
        clearable
        :placeholder="t('placeholder.status')"
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>
