<script setup lang="ts">
import { formateToDate } from '@/helps/formate-to-date-type';
import UpAndDownText from '../up-and-down-text.vue';

interface Props {
  name?:string;
  time?:Date | string;
  timeType?:string;
}

withDefaults(defineProps<Props>(), {
  name: undefined,
  time: undefined,
  timeType: 'dateTime',
});

</script>


<template>
  <UpAndDownText
    :up="name"
    :down="formateToDate(time,timeType)"
  />
</template>
