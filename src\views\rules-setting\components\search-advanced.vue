<script lang='ts' setup>
import type { SearchAdvancedModelType } from '^/types/lab-manage';

import { namespaceT } from '@/helps/namespace-t';

import WrapperSearchAdvanced from '@/components/common/wrapper-search-advanced.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import PimaInput from '@/components/common/pima-input.vue';
import SelectAbleStatus from '@/components/biz/select/able-status.vue';
import SelectManagementUnit from '@/components/biz/select/management-unit.vue';
import SelectLabType from '@/components/biz/select/lab-type.vue';


const emit = defineEmits<{
  'reset': []
}>();

const model = defineModel<SearchAdvancedModelType>();

const t = namespaceT('labManage');
const tc = namespaceT('common');

function onReset() {
  emit('reset');
}

</script>


<template>
  <WrapperSearchAdvanced
    v-bind="$attrs"
    @reset="onReset"
  >
    <Row :gutter="70">
      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 实验室类型 -->
        <PairLabelItem :label="t('label.type')">
          <SelectLabType
            v-model="model.name"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 管理单位 -->
        <PairLabelItem :label="t('label.unitForManagement')">
          <SelectManagementUnit
            v-model="model.name"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 状态 -->
        <PairLabelItem :label="t('label.status')">
          <SelectAbleStatus
            v-model="model.name"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 实验名称 -->
        <PairLabelItem :label="t('label.name')">
          <PimaInput
            v-model.trim="model.name"
            clearable
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 管理员姓名 -->
        <PairLabelItem :label="t('label.adminName')">
          <PimaInput
            v-model.trim="model.name"
            clearable
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 管理员工号 -->
        <PairLabelItem :label="t('label.adminNo')">
          <PimaInput
            v-model.trim="model.name"
            clearable
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 负责人姓名 -->
        <PairLabelItem :label="t('label.principalName')">
          <PimaInput
            v-model.trim="model.name"
            clearable
          />
        </PairLabelItem>
      </Col>

      <Col
        :xxl="6"
        :xl="8"
      >
        <!-- 负责人工号 -->
        <PairLabelItem :label="t('label.principalNo')">
          <PimaInput
            v-model.trim="model.name"
            clearable
          />
        </PairLabelItem>
      </Col>
    </Row>
  </WrapperSearchAdvanced>
</template>
