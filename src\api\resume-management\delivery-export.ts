import { CommonApi } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';


enum ErrorCodes {
  EXPORT_DATA_LIMIT = 'EXPORT_DATA_LIMIT',
  CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT = 'CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT',
  NO_EXISTS = 'NO_EXISTS',
}

export class DeliveryExportApi extends CommonApi<{ model:number }> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-post-resumes/${this.id}/export`;
  }

  async send(): Promise<{ model:number }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.resumeMgt.export');

      switch (error.code) {
        case ErrorCodes.EXPORT_DATA_LIMIT:
        case ErrorCodes.CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT:
        case ErrorCodes.NO_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
