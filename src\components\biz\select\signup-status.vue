<script setup lang="ts">
import { useSignUpStatusStore } from '@/store/data-tags/signup-status';
import { onBeforeMount } from 'vue';


const store = useSignUpStatusStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
