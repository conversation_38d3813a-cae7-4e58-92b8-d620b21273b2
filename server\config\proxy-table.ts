module.exports = function createProxyTable(apis) {
  const urls = {
    [apis.LOCAL_BDC_CORE_API_BASE_URL]: process.env.BDC_CORE_API_BASE_URL,
    [apis.LOCAL_BDC_CLIENT_API_BASE_URL]: process.env.BDC_CLIENT_API_BASE_URL,
    [apis.LOCAL_BDC_ARCH_API_BASE_URL]: process.env.BDC_ARCH_API_BASE_URL,
    [apis.LOCAL_BDC_SERVICE_API_BASE_URL]: process.env.BDC_SERVICE_API_BASE_URL,
    [apis.LOCAL_BDC_DFS_API_BASE_URL]: process.env.BDC_DFS_API_BASE_URL,
    [apis.LOCAL_STATIC_RESOURCES_BASE_URL]: process.env.STATIC_RESOURCES_BASE_URL,
    [apis.LOCAL_BDC_EXPORT_API_BASE_URL]: process.env.BDC_EXPORT_API_BASE_URL,
    [apis.LOCAL_BDC_IMPORT_API_BASE_URL]: process.env.BDC_IMPORT_API_BASE_URL,
    [apis.LOCAL_ENGINEERING_LAB_API_BASE_URL]: process.env.ENGINEERING_LAB_API_BASE_URL,
  };

  const config = {};
  Object.entries(urls).forEach(([localUrl, remoteUrl]) => {
    if (!remoteUrl) {
      throw new Error(`[proxy-table] remoteUrl not configured for ${localUrl}`);
    }

    Object.assign(config, {
      [localUrl]: {
        target: remoteUrl,
        pathRewrite: {
          [localUrl]: '',
        },
        changeOrigin: true,
        xfwd: true,
        logLevel: 'debug',
      },
    });
  });

  return config;
};
