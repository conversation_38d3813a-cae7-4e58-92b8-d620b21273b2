
import { createInputRules, createSelectRules, createAttachmentRules } from '@/helps/rules';

export const createFormRules = () => {
  return {
    /** 实验室名称 */
    name: [createInputRules()],
    /** 实验室类型 */
    type: [createSelectRules()],
    /** 管理单位 */
    unitForManagement: [createSelectRules()],
    /** 管理员 */
    admin: [createSelectRules({ type: 'object' })],
    /** 负责人 */
    principal: [createSelectRules({ type: 'object' })],
    /** 实验室简介 */
    introduction: [createInputRules()],
    /** 状态 */
    status: [createSelectRules()],
  };
};

export const createTypeFormRules = () => {
  return {
    /** 类型中文名称 */
    typeCNName: [createInputRules()],
    /** 类型英文名称 */
    typeENName: [createInputRules()],
    /** 封面图片 */
    cover: [createAttachmentRules()],
    /** 类型中文介绍 */
    typeCNIntroduction: [createInputRules()],
    /** 类型英文介绍 */
    typeENIntroduction: [createInputRules()],
    /** 状态 */
    status: [createSelectRules()],
  };
};
