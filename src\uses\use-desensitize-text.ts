import { EmptyText } from '@/consts/empty-text';
import { desensitizeMobile } from '@/helps/desensitize';
import { isNothing } from '@/utils/is';
import { computed, ref } from 'vue';


export const useDesensitizeText = (str: string | string[]) => {
  const sensitiveText = ref< string[]>(Array.isArray(str) ? str : [str]);
  /** 默认加密状态 */
  const isSensitive = ref(true);

  /** 传入的数据 是否为空 */
  const isEmpty = computed(() => {
    return sensitiveText.value.every((item) => isNothing(item));
  });

  const insensitiveText = computed(() => {
    return sensitiveText.value.map((item) => desensitizeMobile(item));
  });


  /** 处理后的文案 */
  const textByHandled = computed(() => {
    if (isEmpty.value) {
      return Array.from({ length: sensitiveText.value.length }).map(() => EmptyText);
    }

    let textArr : string[] = !isSensitive.value ? sensitiveText.value : insensitiveText.value;

    textArr = textArr.map((item) => {
      return isNothing(item) ? EmptyText : item;
    });

    return textArr;
  });

  /** 切换状态 */
  const onToggleStatus = () => {
    if (isEmpty.value) {
      return;
    }

    isSensitive.value = !isSensitive.value;
  };


  return {
    textByHandled,
    isEmpty,

    onToggleStatus,
  };
};
