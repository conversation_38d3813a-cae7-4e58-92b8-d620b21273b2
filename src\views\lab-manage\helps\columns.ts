import type { ColumnsType } from '^/types/columns';

import { namespaceT } from '@/helps/namespace-t';

export const createColumns = (): ColumnsType[] => {
  const t = namespaceT('labManage.columns');
  const tc = namespaceT('common.table');

  return [
    // 序号
    {
      title: tc('serial'),
      type: 'index',
      align: 'center',
      width: 60,
    },

    // 实验室信息
    {
      title: t('info'),
      slot: 'info',
      minWidth: 100,
    },

    // 实验室类型
    {
      title: t('type'),
      slot: 'type',
      minWidth: 100,
    },

    // 实验室面积
    {
      title: t('area'),
      slot: 'area',
      minWidth: 120,
    },

    // 容纳人数
    {
      title: t('capacity'),
      slot: 'capacity',
      width: 100,
    },

    // 管理员
    {
      title: t('admin'),
      slot: 'admin',
      minWidth: 100,
    },

    // 负责人
    {
      title: t('principal'),
      slot: 'principal',
      minWidth: 100,
    },

    // 简介
    {
      title: t('introduction'),
      slot: 'introduction',
      minWidth: 100,
    },

    // 备注
    {
      title: t('remark'),
      slot: 'remark',
      minWidth: 100,
    },


    // 状态
    {
      title: t('status'),
      slot: 'status',
      width: 80,
    },

    // 操作人/时间
    {
      title: t('operatorAndTime'),
      slot: 'operatorAndTime',
      minWidth: 100,
    },


    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};


export const createColumnsForType = (): ColumnsType[] => {
  const t = namespaceT('labManage.columns');
  const tc = namespaceT('common.table');

  return [
    // 序号
    {
      title: tc('serial'),
      type: 'index',
      align: 'center',
      width: 60,
    },

    // 实验室类型名称
    {
      title: t('typeName'),
      slot: 'typeName',
      minWidth: 100,
    },

    // 备注
    {
      title: t('remark'),
      slot: 'remark',
      minWidth: 100,
    },

    // 状态
    {
      title: t('status'),
      slot: 'status',
      width: 80,
    },

    // 操作人/时间
    {
      title: t('operatorAndTime'),
      slot: 'operatorAndTime',
      minWidth: 100,
    },


    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};
