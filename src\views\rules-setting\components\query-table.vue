<script lang="ts" setup>
import { getCurrentInstance, onBeforeMount } from 'vue';

import type { LabManageListItemType } from '^/types/lab-manage';
import type { Auth } from '@/config/auth';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import DefaultText from '@/components/common/default-text.vue';
import TextEllipsis from '@/components/common/text-ellipsis.vue';
import CellLabInfo from '@/components/biz/table-cell/lab-info.vue';
import CellLabType from '@/components/biz/table-cell/lab-type.vue';
import CellLabArea from '@/components/biz/table-cell/lab-area.vue';
import CellAdmin from '@/components/biz/table-cell/admin.vue';
import CellNameAndTime from '@/components/biz/table-cell/name-and-time.vue';


import EditIcon from '@/assets/img/table-action/edit.png';
import DeleteIcon from '@/assets/img/table-action/delete.png';

import { namespaceT } from '@/helps/namespace-t';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { createColumns } from '../helps/columns';
import { getAbleStatusColor } from '@/helps/color/able-status';
import { getAbleStatusText } from '@/helps/i18n/able-status';


defineProps<{
  data: LabManageListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-edit': [id:number]
  'on-delete': [id:number, labName:string]
}>();


const vm = getCurrentInstance();
const t = namespaceT('labManage');
const columns = createColumns();

const postTypeStore = usePostTypeStore();

const can = (type: string) => {
  return vm?.proxy?.$can?.((P: typeof Auth) => P.LabManage?.[type]) ?? false;
};


const actions = [
  {
    label: t('action.edit'),
    icon: EditIcon,
    triggerEvent: (row: LabManageListItemType) => emit('on-edit', row.id),
    can: (row: LabManageListItemType) => row.canUpdate && can('Edit'),
  },

  {
    label: t('action.delete'),
    icon: DeleteIcon,
    triggerEvent: (row: LabManageListItemType) => emit('on-delete', row.id, row.labName),
    can: (row: LabManageListItemType) => row.canRemove && can('Delete'),
  },
];


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
});

</script>


<template>
  <CTable
    :columns="columns"
    :data="[{id:1,canUpdate:true,canRemove:true,labName:'test'}]"
    :loading="loading"
  >
    <!-- 实验室信息 -->
    <template #info="{ row }">
      <CellLabInfo
        :name="'1'"
        :management-unit="'2'"
        :position="'3'"
      />
    </template>

    <!-- 实验室类型 -->
    <template #type="{ row }">
      <CellLabType :type="'1'" />
    </template>

    <!-- 实验室面积 -->
    <template #area="{ row }">
      <CellLabArea :area="row.area" />
    </template>

    <!-- 容纳人数 -->
    <template #capacity="{ row }">
      <DefaultText :text="row.capacity" />
    </template>

    <!-- 管理员 -->
    <template #admin="{ row }">
      <CellAdmin
        :key="row.id+row.name+row.no+row.mobile"
        :name="row.name"
        :no="row.no"
        :mobile="'***********'"
      />
    </template>

    <!-- 负责人 -->
    <template #principal="{ row }">
      {{ row.principalName }}({{ row.no }})
    </template>

    <!-- 简介 -->
    <template #introduction="{ row }">
      <TextEllipsis
        :value="'啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的'"
        :max-lines="3"
      >
        <DefaultText :text="'啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的啊实打实的'" />
      </TextEllipsis>
    </template>

    <!-- 备注 -->
    <template #remark="{ row }">
      <DefaultText :text="row.remark" />
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <div
        class="pima-status"
        :class="getAbleStatusColor(row.status)"
      >
        {{ getAbleStatusText(row.status) }}
      </div>
    </template>

    <!-- 操作人/时间 -->
    <template #operatorAndTime="{ row }">
      <CellNameAndTime
        :name="row.name"
        :time="row.name"
      />
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>
</template>
